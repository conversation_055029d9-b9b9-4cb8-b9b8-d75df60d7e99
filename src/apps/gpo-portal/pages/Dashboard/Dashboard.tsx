import { Text } from '@mantine/core';
import { PlatformUsageRateCard } from '../../components/PlatformUsageRateCard/PlatformUsageRateCard';
import { SpendAnalysisCard } from '../../components/SpendAnalysisCard/SpendAnalysisCard';
import { VendorsOverviewCard } from '../../components/VendorsOverviewCard/VendorsOverviewCard';

export const Dashboard = () => {
  return (
    <div className="h-screen bg-[#FAFAFA] px-[1.5rem] py-[2rem]">
      <Text size="xl" fw={700} mb="2rem">
        GPO Dashboard
      </Text>

      <div className="mb-8 flex gap-4">
        <SpendAnalysisCard className="flex-1" />
        <PlatformUsageRateCard className="flex-1" />
      </div>

      <VendorsOverviewCard />
    </div>
  );
};
