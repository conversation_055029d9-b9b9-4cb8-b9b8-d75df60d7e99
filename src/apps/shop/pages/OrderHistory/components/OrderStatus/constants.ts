import type { ShippingStatusType } from '@/libs/orders/types';
import i18n from '@/apps/shop/i18n';
import Box from './assets/box.svg?react';
import Checked from './assets/confirm-check.svg?react';
import Truck from './assets/truck.svg?react';
import ErrorIcon from './assets/error.svg?react';
import BackOrder from './assets/back-order-icon.svg?react';
import NonTrackable from './assets/non-track.svg?react';

interface ConfigType {
  label: string;
  color: string;
  Icon: React.FC<{ className: string }> | null;
  step: 1 | 2 | 3 | 4 | 5 | null;
}

export const ORDER_STATUS_CONFIGS: Record<ShippingStatusType, ConfigType> = {
  PENDING: {
    label: i18n.t('client.orderHistory.PENDING'),
    color: '#826D29',
    Icon: Box,
    step: 1,
  },
  PROCESSING: {
    label: i18n.t('client.orderHistory.PROCESSING'),
    color: '#826D29',
    Icon: Box,
    step: 2,
  },
  PARTIALLY_SHIPPED: {
    label: i18n.t('client.orderHistory.PARTIALLY_SHIPPED'),
    color: '#2857AA',
    Icon: Truck,
    step: 3,
  },
  SHIPPED: {
    label: i18n.t('client.orderHistory.SHIPPED'),
    color: '#2857AA',
    Icon: Truck,
    step: 4,
  },
  PARTIALLY_DELIVERED: {
    label: i18n.t('client.orderHistory.PARTIALLY_DELIVERED'),
    color: '#2857AA',
    Icon: Truck,
    step: 4,
  },
  DELIVERED: {
    label: i18n.t('client.orderHistory.DELIVERED'),
    color: '#225A0F',
    Icon: Checked,
    step: 5,
  },
  PLACEMENT_FAILED: {
    label: i18n.t('client.orderHistory.PLACEMENT_FAILED'),
    color: '#826D29',
    Icon: Box,
    step: null,
  },
  ACCEPTED: {
    label: i18n.t('client.orderHistory.ACCEPTED'),
    color: '#826D29',
    Icon: Box,
    step: null,
  },
  BACKORDERED: {
    label: i18n.t('client.orderHistory.BACKORDERED'),
    color: '#8C1139',
    Icon: BackOrder,
    step: null,
  },
  REJECTED: {
    label: i18n.t('client.orderHistory.REJECTED'),
    color: '#8C1139',
    Icon: ErrorIcon,
    step: null,
  },
  RETURNED: {
    label: i18n.t('client.orderHistory.RETURNED'),
    color: '#8C1139',
    Icon: ErrorIcon,
    step: null,
  },
  CANCELLED: {
    label: i18n.t('client.orderHistory.CANCELLED'),
    color: '#8C1139',
    Icon: ErrorIcon,
    step: null,
  },
  NON_TRACKABLE: {
    label: i18n.t('client.orderHistory.NON_TRACKABLE'),
    color: '#5A5C66',
    Icon: NonTrackable,
    step: null,
  },
};
