import * as Yup from 'yup';
import i18n from '@/apps/shop/i18n';

export const SCHEMA = Yup.object().shape({
  password: Yup.string()
    .required(() => i18n.t('form.errorMessage.required', { path: 'Password' }))
    .min(8, ({ min }) =>
      i18n.t('form.errorMessage.min', { path: 'Password', min }),
    ),
  confirmPassword: Yup.string()
    .required(() =>
      i18n.t('form.errorMessage.required', { path: 'Confirm password' }),
    )
    .min(8, ({ min }) =>
      i18n.t('form.errorMessage.min', { path: 'Confirm password', min }),
    )
    .oneOf([Yup.ref('password')], i18n.t('form.errorMessage.matchPassword')),
});
