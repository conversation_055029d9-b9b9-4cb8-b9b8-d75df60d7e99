import { getPriceString } from '@/utils';

interface PromotionSummaryProps {
  paidItemsQty: number;
  freeItemsQty: number;
  subtotalPaidItems: number;
  subtotalAllItems: number;
}

export const PromotionSummary = ({
  paidItemsQty,
  freeItemsQty,
  subtotalPaidItems,
  subtotalAllItems,
}: PromotionSummaryProps) => {
  return (
    <div className="flex w-full items-center justify-between">
      <span className="text-xs text-neutral-600">
        You&apos;re getting total of{' '}
        <strong>{paidItemsQty + freeItemsQty}</strong> products
      </span>
      <span className="text-xs text-neutral-500">
        Promotional Savings:{' '}
        <span className="text-sm font-medium text-black">
          {getPriceString(subtotalAllItems - subtotalPaidItems)}
        </span>
      </span>
    </div>
  );
};
