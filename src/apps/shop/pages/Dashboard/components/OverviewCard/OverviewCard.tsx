import React from 'react';
import { useTranslation } from 'react-i18next';
import { Text } from '@mantine/core';
import clsx from 'clsx';

import TypeIcon from '@/assets/images/dashboard/type.svg?react';
import { Tooltip } from '@/libs/ui/Tooltip';

import { OverviewType } from '../../types';
import styles from './overview-card.module.css';

type OverviewCard = OverviewType;

export const OverviewCard = (prosp: OverviewCard) => {
  const { title, value, additionValue, customIcon } = prosp;

  const { t } = useTranslation();

  return (
    <div className={styles.item}>
      <Text className={styles.title} size="lgMd" fw={700} c="dark.7">
        {title}
      </Text>

      <Text className={styles.value} size="xl" fw={700} c="dark.7">
        {value}
      </Text>

      {additionValue ? (
        <Text className={styles.additionValue} size="sm" c="dark.5">
          {additionValue}
        </Text>
      ) : null}

      {customIcon ? (
        <div className={styles.icon}>{customIcon}</div>
      ) : (
        <div className={clsx(styles.icon)}>
          <Tooltip label={t('common.comingSoon')}>
            <TypeIcon />
          </Tooltip>
        </div>
      )}
    </div>
  );
};
