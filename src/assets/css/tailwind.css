@import 'tailwindcss';

@theme {
  --font-sans: 'Open Sans', sans-serif;
}

@layer utilities {
  .text-sxs {
    font-size: 0.625rem;
    line-height: 1.6;
  }
}

@layer components {
  .main {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 2rem 1.5rem;
    max-width: 2000px;

    .pageContent {
      width: 100%;
      box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.05);
      border-radius: 0.5rem;
      position: relative;
      background-color: #fff;
    }
  }
  .divider-h {
    border-top: 1px solid rgb(0 0 0 / 0.1);
  }

  .divider-v {
    border-left: 1px solid rgb(0 0 0 / 0.1);
    height: 1rem;
    margin: 0 0.5rem;
  }
}
