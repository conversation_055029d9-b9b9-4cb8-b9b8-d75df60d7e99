import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation, Trans } from 'react-i18next';
import * as Yup from 'yup';

import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { AuthFormWrapper } from '../AuthFormWrapper/AuthFormWrapper';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler } from '@/utils';
import { ApiErrorProps } from '@/types/utility';
import styles from './ResetPasswordForm.module.css';

interface ResetPasswordFormProps {
  onSuccess: (userData: unknown) => void;
  onError?: (error: ApiErrorProps) => void;
  apiFunc: (values: FormValues) => Promise<unknown>;
  isLoading?: boolean;
  schema: Yup.ObjectSchema<FormValues>;
  namespace?: string;
}

export interface FormValues {
  password: string;
  confirmPassword: string;
}

export const ResetPasswordForm = ({
  onSuccess,
  onError,
  apiFunc,
  isLoading: externalLoading = false,
  schema,
  namespace = 'changePassword',
}: ResetPasswordFormProps) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const { apiRequest: handleChangePassword, isLoading: internalLoading } =
    useAsyncRequest({
      apiFunc: async (values?: FormValues) => {
        if (!values) return;
        const result = await apiFunc(values);
        onSuccess(result);
      },
      errorFunc: (error) => {
        defaultFormErrorHandler(error, setError);
        onError?.(error);
      },
    });

  const isLoading = externalLoading || internalLoading;

  const handleChangePasswordSubmit = handleSubmit((values) => {
    handleChangePassword(values);
  });

  return (
    <AuthFormWrapper
      title={t(`${namespace}.title`)}
      subtitle={
        <Trans
          i18nKey={`${namespace}.subtitle`}
          components={{ b: <b />, br: <br /> }}
          values={{ email: '' }}
        />
      }
    >
      <form onSubmit={handleChangePasswordSubmit}>
        <Input
          type="password"
          label={t('form.field.password')}
          placeholder={t('form.field.password')}
          {...register('password')}
          disabled={isLoading}
          size="md"
          error={errors.password?.message}
        />

        <Input
          type="password"
          label={t('form.field.confirmPassword')}
          placeholder={t('form.field.confirmPassword')}
          {...register('confirmPassword')}
          disabled={isLoading}
          size="md"
          error={errors.confirmPassword?.message}
        />

        <Button
          type="submit"
          loading={isLoading}
          className={styles.submitButton}
        >
          {t(`${namespace}.confirm`)}
        </Button>
      </form>
    </AuthFormWrapper>
  );
};
