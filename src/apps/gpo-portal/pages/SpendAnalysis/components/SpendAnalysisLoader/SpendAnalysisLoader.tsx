import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

interface SpendAnalysisLoaderProps {
  count?: number;
}

export const SpendAnalysisLoader = ({
  count = 5,
}: SpendAnalysisLoaderProps) => {
  return (
    <div className="flex flex-col gap-2">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="h-[102px] overflow-hidden rounded border border-black/[0.04] bg-white p-4"
        >
          <div className="flex h-full items-start gap-3">
            <div className="flex h-full flex-shrink-0 items-center">
              <Skeleton width={16} height={16} />
            </div>

            <div className="flex h-full flex-1 flex-col justify-between">
              <div className="flex items-start justify-between">
                <div className="flex flex-col gap-1">
                  <div className="mb-1 flex items-center gap-2">
                    <Skeleton width={120} height={12} />
                    <Skeleton width={50} height={12} />
                  </div>
                  <Skeleton width={180} height={16} />
                  <div className="mb-1 flex items-center gap-2">
                    <Skeleton width={120} height={12} />
                    <Skeleton width={50} height={12} />
                  </div>
                </div>
                <div className="flex flex-1 flex-col gap-1 px-6">
                  <Skeleton width="100%" height={60} />
                </div>

                <div className="flex h-full flex-shrink-0 items-center">
                  <Skeleton width={16} height={16} circle />
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
