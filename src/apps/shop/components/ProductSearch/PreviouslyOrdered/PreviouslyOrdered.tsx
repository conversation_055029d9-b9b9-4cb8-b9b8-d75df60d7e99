import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';

import { StockStatusIcon } from '@/libs/products/components/StockStatusIcon/StockStatusIcon';
import { LastOrderedOn } from '@/libs/ui/LastOrderedOn/LastOrderedOn';
import { useProductSuggestions } from '../useProductSuggestions';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { useNavigate } from 'react-router-dom';
import { useComboboxContext } from '@/libs/ui/Combobox/ComboboxContext';
import { AddToCart } from '@/libs/ui/AddToCart/AddToCart';

type NavigateProps = {
  productId: string;
  productOfferId: string;
};

export const PreviouslyOrdered = () => {
  const { query } = useProductStore();
  const { previouslyOrderedItems } = useProductSuggestions(query);
  const { closeCombobox } = useComboboxContext();
  const navigate = useNavigate();

  const handleKeyDown =
    ({ productId, productOfferId }: NavigateProps) =>
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        goToProductPage({ productId, productOfferId });
      }
    };

  const goToProductPage = ({ productId, productOfferId }: NavigateProps) => {
    navigate(getProductUrl(productId, productOfferId));
    closeCombobox();
  };

  return (
    <>
      {previouslyOrderedItems.map((item) => (
        <div
          key={item.productOfferId}
          className="grid w-full grid-cols-[1fr_100px] items-center rounded-[4px] border-[0.5px] border-gray-100 bg-gray-50 p-2 hover:bg-white hover:shadow-md hover:ring hover:ring-blue-400"
        >
          <div className="mr-8">
            <div
              onKeyDown={handleKeyDown({
                productId: item.productId,
                productOfferId: item.productOfferId,
              })}
              className="hover:underline"
              tabIndex={0}
              role="option"
              onClick={() =>
                goToProductPage({
                  productId: item.productId,
                  productOfferId: item.productOfferId,
                })
              }
            >
              <p className="mb-1 max-w-96 text-xs font-medium text-wrap text-black">
                {item.productName}
              </p>
            </div>
            <div className="text-sxs flex items-center gap-1">
              {!!item.stockStatus && (
                <div className="[&_svg]:w-5">
                  <StockStatusIcon status={item.stockStatus} />
                </div>
              )}
              <div className="divider-v"></div>
              <span className="text-sxs text-xs text-gray-700">
                {item.vendorName}
              </span>
              <div className="divider-v"></div>
              <div className="text-gray-700 [&_svg]:w-3">
                <LastOrderedOn
                  lastOrderedQuantity={item.quantity}
                  lastOrderedAt={item.lastOrderedAt}
                  size="sxs"
                />
              </div>
            </div>
          </div>
          <AddToCart
            productOfferId={item.productOfferId}
            minIncrement={item.increments || 1}
            size="sm"
          />
        </div>
      ))}
    </>
  );
};
