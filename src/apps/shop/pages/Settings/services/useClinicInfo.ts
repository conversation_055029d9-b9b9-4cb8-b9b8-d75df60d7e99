import { fetchApi } from '@/libs/utils/api';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useCallback, useEffect, useState } from 'react';
import { ClinicInfoType } from '../types';
import { errorNotification } from '@/utils';

export const useClinicInfo = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [clinicInfo, setClinicInfo] = useState<ClinicInfoType | null>(null);

  const { activeClinic } = useAccountStore();

  const fetchClinicInfo = useCallback(async () => {
    setIsLoading(true);

    try {
      const response = await fetchApi<ClinicInfoType>(
        `/clinics/${activeClinic?.id}`,
      );

      setClinicInfo(response);
    } catch {
      errorNotification();
    }
    setIsLoading(false);
  }, [activeClinic?.id]);

  useEffect(() => {
    if (!clinicInfo) {
      fetchClinicInfo();
    }
  }, [clinicInfo, fetchClinicInfo]);

  return {
    fetchClinicInfo,
    clinicInfo,
    isLoading,
  };
};
