import dayjs from 'dayjs';
import clsx from 'clsx';
import { OrderHistoryItemType } from '@/libs/orders/types';
import { Divider, Text, UnstyledButton } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import styles from './OrderHistoryItem.module.css';
import { Link } from 'react-router-dom';
import { getOrderHistoryItemUrl } from '@/apps/shop/routes/utils';
import { DEFAULT_DISPLAY_DATE_FORMAT, FEATURE_FLAGS } from '@/constants';
import { getPriceString } from '@/utils';
import { OrderStatus } from '../OrderStatus/OrderStatus';
import { Icon } from '@/libs/icons/Icon';

interface OrderHistoryItemProps {
  order: OrderHistoryItemType;
  isActive: boolean;
}

export const OrderHistoryItem = ({
  order,
  isActive,
}: OrderHistoryItemProps) => {
  const {
    backorderedItemsCount,
    id,
    date,
    itemsCount,
    orderNumber,
    status,
    totalPrice,
    vendorsCount,
  } = order;
  return (
    <Link
      to={getOrderHistoryItemUrl(id)}
      className={clsx(styles.container, { [styles.active]: isActive })}
    >
      <div className="px-8 py-4">
        <Flex justify="space-between">
          <div>
            <Text c="#666" size="xs">
              Order ID
            </Text>
            <Text c="#333" size="lgMd" fw="500">
              {orderNumber}
            </Text>
          </div>
          <div>
            <Text c="#666" size="xs" ta="right">
              Order Total
            </Text>
            <Text c="#333" size="lgMd" fw="500" ta="right">
              {getPriceString(totalPrice)}
            </Text>
          </div>
        </Flex>
        <Divider my="md" />
        <div className="flex gap-16">
          <div className="flex flex-col text-nowrap">
            <Text c="#333" size="xs" fw="700" mb="0.25rem" mr="0.25rem">
              <Text span c="#666" size="xs" fw="400">
                Date:
              </Text>
              {' ' + dayjs(date).format(DEFAULT_DISPLAY_DATE_FORMAT)}
            </Text>
            <Text c="#333" size="xs" fw="700" mb="0.25rem" mr="0.25rem">
              <Text span c="#666" size="xs" fw="400">
                Total Line Items:
              </Text>
              {' ' + itemsCount}
            </Text>
            <Text c="#333" size="xs" fw="700" mb="0.25rem" mr="0.25rem">
              <Text span c="#666" size="xs" fw="400">
                Total vendors:
              </Text>
              {' ' + vendorsCount}
            </Text>
            {!!backorderedItemsCount && (
              <span className="mr-1 mb-1 flex items-center gap-1 text-xs font-bold">
                <Text span c="#666" size="xs" fw="400">
                  Backordered items:
                </Text>
                {' ' + backorderedItemsCount}
                <Icon name="alert" color="#F14336" size="0.7rem" />
              </span>
            )}
          </div>
          <Flex direction="column" align="flex-end">
            <OrderStatus status={status} align="right" showStepProgress />
            {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
              <>
                <UnstyledButton mt="0.375rem">
                  <Text td="underline" c="#333" size="xs">
                    See Details
                  </Text>
                </UnstyledButton>
              </>
            )}
          </Flex>
        </div>
      </div>
    </Link>
  );
};
