import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { MultiSelect } from '@/libs/form/MultiSelect';
import { Select } from '@/libs/form/Select';
import { Button } from '@/libs/ui/Button/Button';
import { yupResolver } from '@hookform/resolvers/yup';
import { Divider, Text } from '@mantine/core';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { getConnectedVendorOptionsByType } from './utils';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { fetchApi } from '@/libs/utils/api';
import { SCHEMA } from './contants';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';

interface VendorsSettingsFormProps {
  clinicId: string;
  onComplete: VoidFunction;
  defaultValues: {
    primaryDistributorId: string;
    secondaryDistributorId: string;
    preferredManufacturerIds: string[];
  };
}
export const VendorsSettingsForm = ({
  clinicId,
  onComplete,
  defaultValues,
}: VendorsSettingsFormProps) => {
  const {
    vendors,
    getVendors,
    isLoading: isVendorsLoading,
  } = useVendorsStore();

  useEffect(() => {
    getVendors();
  }, [getVendors]);

  const { manufactureresOptions, distributorsOptions } =
    getConnectedVendorOptionsByType(vendors);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
  } = useForm({
    resolver: yupResolver(SCHEMA),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues,
  });

  const { apiRequest: handleUpdateVendorsSettings, isLoading } =
    useAsyncRequest({
      apiFunc: handleSubmit(async (data) => {
        await fetchApi(`/clinics/${clinicId}`, {
          method: 'PATCH',
          body: {
            ...data,
          },
        });

        successNotification('Vendor settings was updated');
        onComplete();
      }),
      errorFunc: (error) => {
        defaultFormErrorHandler(error, setError);
      },
    });

  if (isVendorsLoading) {
    return (
      <div className="relative p-6">
        <ContentLoader />
      </div>
    );
  }

  return (
    <form onSubmit={handleUpdateVendorsSettings}>
      <div className="rounded border border-black/[0.04] bg-black/[0.02] p-6">
        <div className="rounded border border-black/[0.04] bg-white p-6">
          <Text size="16px" fw="500" c="#344054">
            Select your vendors
          </Text>
          <Divider my="md" />
          <div className="mb-4">
            <Select
              options={distributorsOptions}
              label="Primary Distributor"
              {...register('primaryDistributorId')}
              error={errors.primaryDistributorId?.message}
            />
          </div>
          <div className="mb-4">
            <Select
              options={distributorsOptions}
              label="Secondary Distributor"
              error={errors.secondaryDistributorId?.message}
              {...register('secondaryDistributorId')}
            />
          </div>
          <div className="mb-4">
            <MultiSelect
              options={manufactureresOptions}
              label="Preferred Manufactor"
              {...register('preferredManufacturerIds')}
              defaultValue={defaultValues.preferredManufacturerIds}
            />
          </div>
        </div>
        <Text size="12px" c="#666" mt="12px">
          Attention, changing this preferences will change the way your offers
          are being display and the way they are being shared with you. Please,
          certify you are doing the right changes.
        </Text>
      </div>
      <div className="mt-6">
        <Button loading={isLoading} disabled={!isValid} variant="secondary">
          Save
        </Button>
      </div>
    </form>
  );
};
