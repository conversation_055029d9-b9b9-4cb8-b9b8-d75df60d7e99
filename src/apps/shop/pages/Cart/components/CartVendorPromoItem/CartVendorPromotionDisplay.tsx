import { getPriceString } from '@/utils';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { PromotionItemType } from '@/libs/promotions/types';

type QuantityUpdate = {
  amount: number;
  setError: (message: string) => void;
};

export const CartVendorPromotionDisplay = ({
  item,
}: {
  item: PromotionItemType;
}) => {
  const { addToCart } = useCartStore();
  const offer = item.product.offers.find(
    ({ id }) => id === item.productOfferId,
  );

  const handleQuantityUpdate = ({ amount, setError }: QuantityUpdate) => {
    addToCart({
      offers: [
        {
          productOfferId: item.productOfferId,
          quantity: amount,
        },
      ],
      onError: (message) => {
        setError(message);
      },
    });
  };

  return (
    <div className="grid w-full gap-2.5 py-3">
      <div className="flex items-center gap-12">
        <span className="flex-1 text-xs font-medium">{item.product.name}</span>
        <div className="flex items-center gap-2">
          <span className="text-right text-xs font-medium text-neutral-500">
            {getPriceString(item.subtotal)}
          </span>
          <div className="max-w-24 [&_svg]:w-3">
            <AddToCartInput
              originalAmount={item.quantity}
              minIncrement={offer?.increments || 1}
              onUpdate={handleQuantityUpdate}
              size="sm"
            />
          </div>
        </div>
      </div>
      {item.freeItemsQty > 0 && item.freeOffer && (
        <div className="flex items-center gap-12">
          <span className="flex-1 text-xs font-medium">
            {item.freeOffer.name || item.product.name}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-right text-xs font-medium text-green-700/90">
              Free
            </span>
            <span className="grid h-8 w-24 place-items-center rounded-xs border border-gray-200 bg-gray-100 text-sm">
              {item.freeItemsQty}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
