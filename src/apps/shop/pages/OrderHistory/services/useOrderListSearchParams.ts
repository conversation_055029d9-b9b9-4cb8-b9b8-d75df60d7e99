import { areObjectsEqual } from '@/libs/utils/object';
import { useSearchParams } from 'react-router-dom';

export type OrderListSearchParamsType = {
  backordered: 'true' | 'false';
  page: string;
  query: string;
  perPage: string;
  sort: string;
  dateFromFilter: string;
  dateToFilter: string;
};
export const useOrderListSearchParams = (): [
  OrderListSearchParamsType,
  (params: Partial<OrderListSearchParamsType>) => void,
  searchParamsString: string,
] => {
  const initialSearchParams = {
    perPage: '5',
  };
  const [searchParams, setSearchParams] = useSearchParams(initialSearchParams);

  const setParams = (params: Partial<OrderListSearchParamsType>) => {
    const previousSearchParams = Object.fromEntries(searchParams.entries());
    const newSearchParams = {
      ...previousSearchParams,
      ...params,
    };

    if (!areObjectsEqual(previousSearchParams, newSearchParams)) {
      setSearchParams(newSearchParams);
    }
  };

  return [
    Object.fromEntries(searchParams.entries()) as OrderListSearchParamsType,
    setParams,
    searchParams.toString(),
  ];
};
