import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createStore } from '@/utils';
import { AccountType } from '@/types/common';
import { fetchApi } from '@/libs/utils/api';
import { ActiveClinicProps } from '@/libs/clinics/types';
import { setActiveClinic } from '@/libs/clinics/utils/activeClinic';

interface State {
  account: AccountType | null;
  activeClinic: ActiveClinicProps | null;
}

interface Actions {
  fetchAccountData: (accountId: string) => void;
  updateAccount: (updatedField: Partial<AccountType>) => void;
  setActiveClinic: (data: ActiveClinicProps | null) => void;
}

export const useAccountStore = createStore<State & Actions>()(
  immer(
    devtools((set) => ({
      account: null,
      activeClinic: null,
      clinics: [],
      fetchAccountData: async (accountId) => {
        if (!accountId) {
          return;
        }

        const response = await fetchApi<AccountType>(`/accounts/${accountId}`);

        set({
          account: response,
        });
      },
      updateAccount: (updatedField) => {
        set((state) => ({
          account: { ...(state.account || {}), ...updatedField },
        }));
      },
      setActiveClinic: (data) => {
        setActiveClinic(data);
        set({ activeClinic: data });
      },
    })),
  ),
);
