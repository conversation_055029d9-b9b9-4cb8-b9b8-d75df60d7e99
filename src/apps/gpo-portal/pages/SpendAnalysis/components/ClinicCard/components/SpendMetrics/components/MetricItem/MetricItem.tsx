import React from 'react';
import { PercentageDisplay } from '@/libs/ui/PercentageDisplay';

interface MetricItemProps {
  label: string;
  value: string;
  yoyPercentage: number;
}

export const MetricItem = ({
  label,
  value,
  yoyPercentage,
}: MetricItemProps) => {
  return (
    <div className="flex-1">
      <div className="text-sxs mb-2 font-medium text-[#666]">{label}</div>
      <div className="flex items-center justify-center">
        <div className="text-sm font-medium text-[#344054]">{value}</div>
        <PercentageDisplay
          percentage={yoyPercentage}
          size="md"
          className="ml-2"
          label="(YOY)"
        />
      </div>
    </div>
  );
};
