import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Badge } from '@/libs/ui/Badge/Badge';
import { Button } from '@/libs/ui/Button/Button';

import { useTranslation } from 'react-i18next';

export const EstimateRebateCTA = ({
  currentRebatePercent,
  promotionKeyWord,
}: {
  currentRebatePercent: number;
  promotionKeyWord: string;
}) => {
  const { t } = useTranslation();
  return (
    <Badge className="rounded-lg bg-black/5 py-3">
      <div className="flex items-center gap-8">
        <div className="grid">
          <span className="text-xs font-bold text-nowrap text-[#344054]">
            {t('client.dashboard.startEarning', { currentRebatePercent })}
          </span>
          <span className="text-xs text-[#344054]">
            {t('client.dashboard.unlockRebate')}
          </span>
        </div>
        <Button
          to={`${SHOP_ROUTES_PATH.search}?query=${promotionKeyWord}`}
          size="sm"
          p="0 1.5rem"
        >
          {t('client.dashboard.shopNow')}
        </Button>
      </div>
    </Badge>
  );
};
