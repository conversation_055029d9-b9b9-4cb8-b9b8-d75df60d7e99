import { PropsWithChildren, useEffect, useState } from 'react';
import { Outlet, Navigate } from 'react-router-dom';
import { LoadingOverlay } from '@mantine/core';

import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { fetchApi } from '@/libs/utils/api';
import { UserType } from '@/types/common';
import { ApiErrorProps } from '@/types/utility';
import { apiErrorNotification } from '@/utils';

interface ProtectedLayoutProps extends PropsWithChildren {
  additionApiCall?: (user: UserType) => Promise<void> | void;
}

export const ProtectedLayout = ({
  children,
  additionApiCall,
}: ProtectedLayoutProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const { user, setUser } = useAuthStore();
  const { fetchAccountData } = useAccountStore();

  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userData = await fetchApi<UserType>('/users/me');

        setUser(userData);

        await fetchAccountData(userData.accountId);

        if (additionApiCall) {
          await additionApiCall(userData);
        }
      } catch (err) {
        const { data } = err as ApiErrorProps;

        if (data?.message) {
          apiErrorNotification(data.message);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return <LoadingOverlay visible />;
  }

  if (!user) {
    return <Navigate to={SHOP_ROUTES_PATH.login} />;
  }

  return children || <Outlet />;
};
