import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { fetchApi } from '@/libs/utils/api';
import { AddressType, ClinicType } from '@/types/common';
import { ADDRESS_TYPE } from '@/constants';
import { apiErrorNotification, createStore } from '@/utils';
import { ApiErrorProps } from '@/types/utility';

import { useAuthStore } from './useAuthStore';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import {
  clearActiveClinic,
  getActiveClinic,
} from '@/libs/clinics/utils/activeClinic';

interface UpdateAddressResponse {
  clinicId: string;
  address: AddressType;
  addressType: keyof typeof ADDRESS_TYPE;
}
export interface UpdateAddressParams extends UpdateAddressResponse {
  sameAddress: boolean;
}

interface State {
  loading: boolean;
  clinic: ClinicType | null;
}

interface Actions {
  getClinic: (clinicId?: string) => void;
  clearClinic: () => void;
}

export const useClinicStore = createStore<State & Actions>()(
  immer(
    devtools((set) => ({
      loading: false,
      clinic: null,
      getClinic: async (clinicId) => {
        const user = useAuthStore.getState().user;
        const activeClinic = getActiveClinic();

        if (!user || !(activeClinic || clinicId)) {
          return;
        }

        try {
          const response = await fetchApi<ClinicType>(
            `/clinics/${activeClinic?.id || clinicId}`,
          );

          useAccountStore.getState().setActiveClinic({
            id: response.id,
            name: response.name,
            status: response.onboardingStatus,
            hasAnyVendorConnected: response.hasAnyVendorConnected,
          });

          set({
            clinic: response,
          });
        } catch (err) {
          const { data } = err as ApiErrorProps;

          apiErrorNotification(data.message);
        }
      },
      clearClinic: () => {
        clearActiveClinic();
        useAccountStore.getState().setActiveClinic(null);
        set({ clinic: null });
      },
    })),
  ),
);
