import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';

import { Combobox } from '@/libs/ui/Combobox/Combobox';
import { Icon } from '@/libs/icons/Icon';
import { useProductSuggestions } from '../useProductSuggestions';

export const Suggestions = () => {
  const { query } = useProductStore();
  const { suggestions } = useProductSuggestions(query);

  return (
    <>
      {suggestions.slice(0, 7).map((suggestion) => (
        <Combobox.Option
          key={suggestion}
          value={suggestion}
          className="flex items-center pl-0"
        >
          <Icon
            name="magnifier"
            size="1rem"
            color="#B8B8B8"
            className="mr-3"
            aria-hidden={true}
          />
          <span className="line-clamp-1 text-left text-sm">{suggestion}</span>
        </Combobox.Option>
      ))}
    </>
  );
};
