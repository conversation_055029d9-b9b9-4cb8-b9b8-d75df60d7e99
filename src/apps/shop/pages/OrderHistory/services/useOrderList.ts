import { useCallback, useEffect, useState } from 'react';
import { OrderHistoryItemType } from '@/libs/orders/types';
import {
  type OrderListSearchParamsType,
  useOrderListSearchParams,
} from './useOrderListSearchParams';
import { fetchOrderList } from './fetchOrderList';

type useOrderListProps =
  | {
      limit?: number;
    }
  | undefined;
export const useOrderList = ({ limit }: useOrderListProps = {}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [errorOnLoading, setErrorOnLoading] = useState(false);
  const [searchParams, setSearchParams, searchParamsString] =
    useOrderListSearchParams();

  const [data, setData] = useState<{
    orders: OrderHistoryItemType[];
    total: number;
  }>({
    orders: [],
    total: 0,
  });
  const fetchOrders = useCallback(
    (searchParams: OrderListSearchParamsType) =>
      fetchOrderList({
        searchParams: {
          ...searchParams,
          perPage: limit?.toString() ?? searchParams.perPage,
        },
        beforeStart: () => {
          setIsLoading(true);
          setErrorOnLoading(false);
        },
        onSuccess: (data) => {
          setData(data);
        },
        onError: () => {
          setErrorOnLoading(true);
        },
        afterDone: () => {
          setIsLoading(false);
        },
      }),
    [limit, setData],
  );

  useEffect(() => {
    fetchOrders(searchParams);
    // eslint-disable-next-line
  }, [searchParamsString]);

  return {
    orders: data.orders,
    total: data.total,
    errorOnLoading,
    isLoading,
    queryParams: {
      ...searchParams,
      searchParamsString,
      setBackordered: (backordered: 'true' | 'false') => {
        setSearchParams({ backordered, page: '1' });
      },
      setQuery: (query: string) => {
        setSearchParams({ query, page: '1' });
      },
      setPage: (page: string) => {
        setSearchParams({ page });
      },
      setPerPage: (perPage: string) => {
        setSearchParams({ perPage, page: '1' });
      },
      setSort: (sort: string) => {
        setSearchParams({ sort, page: '1' });
      },
      setDatesFilter: (dates: { from: string; to: string }) => {
        setSearchParams({
          dateFromFilter: dates.from,
          dateToFilter: dates.to,
          page: '1',
        });
      },
    },
  };
};
