import { fetchApi } from '@/libs/utils/api';
import type { OrderHistoryItemType } from '@/libs/orders/types';
import type { GetDataWithPagination } from '@/types/utility';
import { OrderListSearchParamsType } from './useOrderListSearchParams';

interface fetchOrderListProps {
  searchParams: OrderListSearchParamsType;
  beforeStart?: VoidFunction;
  onSuccess?: (data: { orders: OrderHistoryItemType[]; total: number }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
}

// TODO: Make it generic and re-useable
const parseQueryParams = (searchParams: OrderListSearchParamsType) => {
  const {
    backordered,
    query,
    dateFromFilter,
    dateToFilter,
    page,
    perPage,
    // TODO: Add sort
    // sort,
  } = searchParams;

  let queryString = '';
  queryString += backordered === 'true' ? `filter[backordered]=true` : '';
  queryString += query ? `filter[search]=${query}` : '';
  queryString += dateFromFilter ? `&filter[date_from]=${dateFromFilter}` : '';
  queryString += dateToFilter ? `&filter[date_to]=${dateToFilter}` : '';
  queryString += page ? `&page[number]=${page}` : '';
  queryString += perPage ? `&page[size]=${perPage}` : '';

  return queryString;
};

export const fetchOrderList = async ({
  searchParams,
  beforeStart = () => {},
  onSuccess = () => {},
  onError = () => {},
  afterDone = () => {},
}: fetchOrderListProps) => {
  beforeStart();

  const queryString = parseQueryParams(searchParams);
  try {
    const response = await fetchApi<
      GetDataWithPagination<OrderHistoryItemType>
    >(`/orders/?${queryString}`);

    onSuccess({
      orders: response.data,
      total: response.meta.total,
    });
  } catch {
    onError();
  }

  afterDone();
};
