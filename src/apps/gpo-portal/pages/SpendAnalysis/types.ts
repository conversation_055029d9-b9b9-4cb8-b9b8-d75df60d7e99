export interface GpoSpendAnalysisResponse {
  data: GpoClinicData[];
  pagination: PaginationData;
}

export interface PaginationData {
  current_page: number;
  per_page: number;
  total: number;
  last_page: number;
}

export interface GpoClinicData {
  id: string;
  name: string;
  member_since: string;
  status: 'ACTIVE' | 'INACTIVE';
  practice_types: string[];
  fulltime_dvm: number;
  total_exam_rooms: number;

  total_spend: {
    amount: string;
    yoy_percentage: number;
  };

  rebates_earned: {
    amount: string;
    yoy_percentage: number;
  };

  preferred_share: {
    amount: string;
    percentage: number;
    yoy_percentage: number;
  };

  preferred_vendor_spend_share: {
    amount: string;
    total: string;
    percentage: number;
    yoy_percentage: number;
  };

  spend: {
    total_spend: string;
    annual_budget: string;
    previous_year_spend: string;
    preferred_vendor_percentage: number;
    non_preferred_vendor_percentage: number;
  };

  market_share_analysis: {
    gpo_vendor_participation_rate: {
      percentage: number;
      yoy_percentage: number;
      description: string;
    };
    distributors: Array<{
      name: string;
      percentage: number;
    }>;
    vendors: Array<{
      name: string;
      percentage: number;
    }>;
    product_categories: Array<{
      name: string;
      total_percentage: number;
      vendors: Array<{
        name: string;
        percentage: number;
      }>;
    }>;
  };

  total_orders: number;
  active_users: number;
  notifications: number;
  is_inactive: boolean;
  last_order_date: string;
  preferred_vendor_percent: number;
}

export interface SpendAnalysisFilters {
  page?: number;
  per_page?: number;
  date_from?: string;
  date_to?: string;
  quarter?: number;
  year?: number;
  fulltime_dvm_min?: number;
  fulltime_dvm_max?: number;
  total_exam_rooms_min?: number;
  total_exam_rooms_max?: number;
  practice_types?: string[];
  min_spend?: number;
  max_spend?: number;
  min_preferred_vendor_percent?: number;
  max_preferred_vendor_percent?: number;
  min_orders?: number;
  max_orders?: number;
  inactive_only?: boolean;
  order_by?:
    | 'name'
    | 'active_users'
    | 'inactive_users'
    | 'notifications'
    | 'total_spend'
    | 'preferred_vendor_percent';
  direction?: 'asc' | 'desc';
  distributors_limit?: number;
  vendors_limit?: number;
  category_vendors_limit?: number;
}
