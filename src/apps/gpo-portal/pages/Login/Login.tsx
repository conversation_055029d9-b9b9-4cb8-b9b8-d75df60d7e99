import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import {
  type FormValues,
  LoginForm,
} from '@/libs/auth/components/LoginForm/LoginForm';
import { fetchApi } from '@/libs/utils/api';
import { tokenManager } from '@/libs/auth/utils/tokenManager';
import { UserType } from '@/types/common';

import { SCHEMA } from './constants';

export const Login = () => {
  const navigate = useNavigate();

  const loginApiFunc = async (values?: FormValues) => {
    if (!values) return;

    const { email, password } = values;

    const response = await fetchApi<{ user: UserType; access_token: string }>(
      '/gpo/login',
      {
        method: 'POST',
        body: { email, password },
        authStrategy: 'token',
      },
    );

    if (response.access_token) {
      tokenManager.setToken(response.access_token);
    }

    return response.user;
  };

  return (
    <LoginForm
      apiFunc={loginApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.dashboard)}
      forgotPasswordPath={GPO_ROUTES_PATH.forgotPassword}
      schema={SCHEMA}
      namespace="gpo.login"
    />
  );
};
