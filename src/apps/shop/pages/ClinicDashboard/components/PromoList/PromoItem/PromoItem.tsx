import { Button } from '@/libs/ui/Button/Button';
import { Divider } from '@mantine/core';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { DEFAULT_DISPLAY_DATE_FORMAT, MODAL_NAME } from '@/constants';
import styles from './PromoItem.module.css';
import { PromoTitle } from '../PromoTitle/PromoTitle';
import { PromoType } from '@/types/common';
import dayjs from 'dayjs';

export const PromoItem = ({
  benefits,
  description,
  endedAt,
  name,
  offers,
  requirements,
  type,
  vendor,
}: PromoType) => {
  const { openModal } = useModalStore();

  const handleTakeDeal = () => {
    openModal({
      benefits,
      description,
      name: MODAL_NAME.PROMO_MATCHER_PRODUCTS,
      offers,
      promoType: type,
      requirements,
      title: name,
    });
  };

  return (
    <div className="group relative rounded-sm border-1 border-black/2 bg-white p-5 shadow-xs hover:border-blue-400">
      <div className="flex items-center justify-between">
        <div className="flex-7">
          <PromoTitle promoType={type} title={name} />
          <p className="max-h-18 overflow-hidden pt-2 pb-1 text-xs tracking-wider text-black/80 transition-all duration-400 ease-in-out group-hover:max-h-72 hover:overflow-y-auto">
            {description}
          </p>
        </div>

        <div className="flex flex-3 justify-end">
          <Button
            size="sm"
            className={styles.takeDealBtn}
            onClick={handleTakeDeal}
          >
            Take Deal
          </Button>
        </div>
      </div>
      <Divider my="xs" />

      <div className="grid grid-cols-[auto_auto_1fr] gap-3 divide-x-1 divide-solid divide-black/10">
        <span className="pr-2">
          <span className="mr-1 text-xs text-black/60">Vendor:</span>
          <span className="text-xs font-semibold">{vendor.name}</span>
        </span>
        <span className="pr-2">
          <span className="mr-1 text-xs text-black/60">Category:</span>
          <span className="text-xs font-semibold">-</span>
        </span>
        <span className="pr-2">
          <span className="mr-1 text-xs text-black/60">End Date:</span>
          <span className="text-xs font-semibold">
            {dayjs(endedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
          </span>
        </span>
      </div>
    </div>
  );
};
