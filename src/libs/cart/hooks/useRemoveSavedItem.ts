import { fetchApi } from '@/libs/utils/api';
import { SavedItemType } from '@/types';
import { ApiErrorProps } from '@/types/utility';
import { apiErrorNotification } from '@/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';

const removeSavedItem = async (savedItemId: string): Promise<SavedItemType> => {
  return fetchApi<SavedItemType>('/saved-items/remove', {
    method: 'DELETE',
    body: {
      savedItemId,
    },
  });
};

export const useRemoveSavedItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (savedItemId: string) => removeSavedItem(savedItemId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.savedItems.all,
      });
    },
    onError: (error) => {
      const apiError = error as unknown as ApiErrorProps;
      const errorMessage =
        apiError.data?.message || 'Failed to add item to saved items';
      apiErrorNotification(errorMessage);
    },
  });
};
