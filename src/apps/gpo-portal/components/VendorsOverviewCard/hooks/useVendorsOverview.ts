import { useState, useEffect, useCallback } from 'react';
import { fetchApi } from '@/libs/utils/api';
import { VendorsOverviewResponse, VendorsOverviewFilters } from '../types';
import dayjs from 'dayjs';
import { DEFAULT_SERVER_DATE_FORMAT } from '@/constants';

export const useVendorsOverview = (
  initialFilters: VendorsOverviewFilters = {},
) => {
  const [data, setData] = useState<VendorsOverviewResponse>({
    total_spend: 0,
    vendors: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<VendorsOverviewFilters>({
    date_from: dayjs().startOf('year'),
    date_to: dayjs().endOf('year'),
    vendor_type: undefined,
    status: 'all',
    min_spend: undefined,
    max_spend: undefined,
    ...initialFilters,
  });

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetchApi<VendorsOverviewResponse>(
        `/gpo/vendors-overview?date_from=${filters.date_from?.format(
          DEFAULT_SERVER_DATE_FORMAT,
        )}&date_to=${filters.date_to?.format(DEFAULT_SERVER_DATE_FORMAT)}`,
        {
          method: 'GET',
          authStrategy: 'token',
        },
      );

      setData(response);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to fetch vendors overview data';
      setError(errorMessage);
      console.error('Error fetching vendors overview:', err);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  const updateFilters = useCallback(
    (newFilters: Partial<VendorsOverviewFilters>) => {
      setFilters((prev) => ({
        ...prev,
        ...newFilters,
      }));
    },
    [],
  );

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    filters,
    updateFilters,
    refresh,
  };
};
