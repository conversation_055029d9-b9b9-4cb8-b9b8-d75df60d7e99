import clsx from 'clsx';
import { Flex } from '@/libs/ui/Flex/Flex';
import { OrderDetails } from '../OrderDetails/OrderDetails';
import { OrderHistoryItemType } from '@/libs/orders/types';
import styles from './OrderHistoryContent.module.css';
import { OrderHistoryItem } from '../OrderHistoryItem/OrderHistoryItem';

interface OrderHistoryContentProps {
  orders: OrderHistoryItemType[];
  selectedOrderId?: string;
}

export const OrderHistoryContent = ({
  orders,
  selectedOrderId,
}: OrderHistoryContentProps) => {
  return (
    <Flex className={clsx('pageContent', styles.content)}>
      <div className="flex flex-col">
        {orders.map((order) => (
          <OrderHistoryItem
            key={order.id}
            order={order}
            isActive={order.id === selectedOrderId}
          />
        ))}
      </div>
      <div className="relative w-full">
        {selectedOrderId && (
          <OrderDetails key={selectedOrderId} id={selectedOrderId} />
        )}
      </div>
    </Flex>
  );
};
