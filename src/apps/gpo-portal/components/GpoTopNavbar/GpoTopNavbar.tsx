import { Flex, Text, Menu, UnstyledButton } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { TopNavbar } from '@/libs/ui/TopNavbar/TopNavbar';
import { Icon } from '@/libs/icons/Icon';

export const GpoTopNavbar = () => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate(GPO_ROUTES_PATH.login);
  };

  // TODO: Uncomment when API is ready
  // if (!user) {
  //   return null;
  // }

  return (
    <TopNavbar>
      <Flex gap="1rem" align="center" justify="space-between" w="100%">
        <Text fw="600" size="lg">
          GPO Portal
        </Text>

        {user && (
          <Menu width={200} shadow="md" position="right-end">
            <Menu.Target>
              <UnstyledButton>
                <Icon
                  name="moreOptions"
                  color="#333"
                  aria-label="User menu options"
                />
              </UnstyledButton>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item onClick={handleLogout}>Logout</Menu.Item>
            </Menu.Dropdown>
          </Menu>
        )}
      </Flex>
    </TopNavbar>
  );
};
