import { fetchApi } from '@/libs/utils/api';
import dayjs from 'dayjs';
import { SpendAnalysisFilters } from '../types';

export const exportCsv = async (filters: SpendAnalysisFilters) => {
  try {
    const response = await fetchApi(
      `/gpo/spend-analysis/export?${new URLSearchParams(filters as Record<string, string>).toString()}`,
      {
        method: 'GET',
        authStrategy: 'token',
        options: {
          headers: {
            Accept: 'text/csv',
          },
        },
      },
    );

    const csvContent =
      typeof response === 'string' ? response : JSON.stringify(response);
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `spend-analysis-${dayjs().format('YYYY-MM-DD')}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('Export failed:', error);
  }
};
