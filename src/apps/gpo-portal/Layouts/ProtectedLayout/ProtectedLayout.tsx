import { PropsWithChildren, useEffect, useState } from 'react';
import { Outlet, Navigate } from 'react-router-dom';
import { LoadingOverlay } from '@mantine/core';

import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { UserType } from '@/types/common';
import { fetchApi } from '@/libs/utils/api';
import { notifications } from '@/utils/notifications';
import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { tokenManager } from '@/libs/auth/utils/tokenManager';

interface ApiErrorProps {
  data?: {
    message?: string;
  };
}

export const ProtectedLayout = ({ children }: PropsWithChildren) => {
  const { setUser, user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadUserData = async () => {
      if (!tokenManager.hasToken()) {
        setIsLoading(false);
        return;
      }

      try {
        const userData = await fetchApi<UserType>('/gpo/user', {
          method: 'GET',
          authStrategy: 'token',
          options: {
            suppressAuthRedirect: true,
          },
        });

        setUser(userData);
      } catch (err) {
        const { data } = err as ApiErrorProps;
        tokenManager.removeToken();

        if (data?.message) {
          notifications.error({
            title: 'Authentication Error',
            message: data.message,
          });
        } else {
          notifications.error({
            title: 'Authentication Error',
            message: 'Failed to load user data. Please try again.',
          });
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, [setUser]);

  if (isLoading) {
    return <LoadingOverlay visible />;
  }

  if (!user) {
    return <Navigate to={GPO_ROUTES_PATH.login} />;
  }

  return children || <Outlet />;
};
