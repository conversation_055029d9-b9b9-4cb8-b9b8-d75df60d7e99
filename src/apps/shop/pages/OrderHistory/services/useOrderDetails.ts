import { useQuery } from '@tanstack/react-query';
import type { OrderHistoryDetailsType } from '@/libs/orders/types';
import { fetchApi } from '@/libs/utils/api';
import { queryKeys } from '@/libs/query/queryClient';

interface useOrderDetailsProps {
  id: string;
}

const fetchOrderDetails = async (
  id: string,
): Promise<OrderHistoryDetailsType> => {
  return fetchApi<OrderHistoryDetailsType>(`/orders/${id}`);
};

export const useOrderDetails = ({ id }: useOrderDetailsProps) => {
  const {
    data: order,
    isLoading,
    isError: errorOnLoading,
  } = useQuery({
    queryKey: queryKeys.orders.details(id),
    queryFn: () => fetchOrderDetails(id),
    refetchInterval: 10000, // This solves the bug where the first request does not bring promo data, this needs to be originally solved on the backend
  });

  return {
    order: order || null,
    errorOnLoading,
    isLoading,
  };
};
