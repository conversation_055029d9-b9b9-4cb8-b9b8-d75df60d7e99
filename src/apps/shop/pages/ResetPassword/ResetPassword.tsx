import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { ResetPasswordForm } from '@/libs/auth/components/ResetPasswordForm/ResetPasswordForm';
import { fetchApi } from '@/libs/utils/api';
import { successNotification } from '@/utils';

import { SCHEMA } from './constants';

export const ResetPassword = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { token } = useParams();
  const email = searchParams.get('email');

  const changePasswordApiFunc = async (values?: {
    password: string;
    confirmPassword: string;
  }) => {
    if (!values) return;

    await fetchApi('/users/me/password', {
      method: 'PATCH',
      body: {
        ...values,
        email,
        token,
      },
    });

    successNotification('Password was updated');
  };

  return (
    <ResetPasswordForm
      apiFunc={changePasswordApiFunc}
      onSuccess={() => navigate(SHOP_ROUTES_PATH.login)}
      schema={SCHEMA}
    />
  );
};
