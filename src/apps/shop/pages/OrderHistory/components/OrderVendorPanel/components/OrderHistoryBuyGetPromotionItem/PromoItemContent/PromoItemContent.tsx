import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { getPriceString } from '@/utils';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';

interface OrderHistoryItemContentProps {
  item: OrderHistoryDetailItemType;
}

export const PromoItemContent = ({ item }: OrderHistoryItemContentProps) => {
  const isFreeItem = item.totalPrice === '0';

  return (
    <div className="ml-4 flex h-full flex-col justify-between">
      <div>
        <p className="mb-0.5 text-xs font-medium text-gray-600/70">
          Order ID: {item.orderNumber}
        </p>
        <Link
          to={getProductUrl(item.product.id, item.productOfferId)}
          className="no-underline hover:underline"
        >
          <p className="font-medium text-gray-800">{item.product.name}</p>
        </Link>
      </div>
      <div className="mt-2 flex items-center">
        <p className="min-w-[6rem] py-3 pr-3 text-sm text-gray-600">
          Quantity:{' '}
          <span className="font-bold text-gray-800">{item.quantity}</span>
        </p>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <p className="p-3 text-sm text-gray-600">
          Price:{' '}
          {isFreeItem ? (
            <span className="font-bold text-green-700">FREE</span>
          ) : (
            <span className="font-bold text-gray-800">
              {getPriceString(item.unitPrice)}
            </span>
          )}
        </p>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <p className="p-3 text-sm text-gray-600">
          Net Total:{' '}
          {isFreeItem ? (
            <span className="font-bold text-green-700">FREE</span>
          ) : (
            <span className="font-bold text-gray-800">
              {getPriceString(item.totalPrice)}
            </span>
          )}
        </p>
      </div>
    </div>
  );
};
