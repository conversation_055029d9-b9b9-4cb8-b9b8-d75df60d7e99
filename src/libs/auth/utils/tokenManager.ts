// TODO: Improve to accept multiple key tokens
const GPO_TOKEN_KEY = 'gpo_token';

export const tokenManager = {
  getToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(GPO_TOKEN_KEY);
  },

  setToken: (token: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.setItem(GPO_TOKEN_KEY, token);
  },

  removeToken: (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(GPO_TOKEN_KEY);
  },

  hasToken: (): boolean => {
    return !!tokenManager.getToken();
  },

  getAuthHeader: (): string | null => {
    const token = tokenManager.getToken();
    return token ? `Bearer ${token}` : null;
  },
};
