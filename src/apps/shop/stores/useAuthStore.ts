import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createStore } from '@/utils';
import { Account, UserType } from '@/types/common';
import { fetchApi } from '@/libs/utils/api';

interface State {
  user: UserType | null;
}

interface Actions {
  setUser: (user: UserType) => void;
  getGpo: () => Account['gpo'];
  updateUser: (user: Partial<UserType>) => void;
  logout: () => void;
  leaveImpersonation: () => void;
}

export const useAuthStore = createStore<State & Actions>()(
  immer(
    devtools((set, get) => ({
      user: null,
      logout: async () => {
        await fetchApi('/sessions', {
          method: 'DELETE',
          withApi: false,
        });
        set({ user: null });
      },
      leaveImpersonation: async () => {
        const response = await fetchApi<{
          message: string;
          redirectUri: string;
        }>('/users/stop-impersonate', {
          method: 'GET',
          withApi: true,
        });

        if (response.redirectUri) {
          window.location.href = response.redirectUri;
        }
      },
      getGpo: () => {
        return get().user?.account?.gpo ?? null;
      },
      setUser: (user) => set({ user }),
      updateUser: (updateUserField) =>
        set((state) => ({ user: { ...state.user, ...updateUserField } })),
    })),
  ),
);
