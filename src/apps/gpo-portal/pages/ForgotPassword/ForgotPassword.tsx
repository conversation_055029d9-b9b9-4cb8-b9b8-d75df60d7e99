import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { ForgotPasswordForm } from '@/libs/auth/components/ForgotPasswordForm/ForgotPasswordForm';
import { fetchApi } from '@/libs/utils/api';
import { successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

export const ForgotPassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const resetPasswordApiFunc = async (values?: { email: string }) => {
    if (!values) return;

    await fetchApi('/gpo/password-resets', {
      method: 'POST',
      body: values,
    });

    successNotification(t('gpo.forgotPassword.resetMessage'));
  };

  return (
    <ForgotPasswordForm
      apiFunc={resetPasswordApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.login)}
      loginPath={GPO_ROUTES_PATH.login}
      schema={SCHEMA}
      namespace="gpo.forgotPassword"
    />
  );
};
