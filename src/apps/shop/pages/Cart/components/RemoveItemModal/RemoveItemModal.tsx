import { useTranslation, Trans } from 'react-i18next';
import { Button } from '@mantine/core';

import { MODAL_NAME } from '@/constants';
import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { Modal } from '@/components';
import BinIcon from '@/assets/images/cart/circled-bin.svg?react';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import styles from '../../Cart.module.css';

type RemoveModalOptions = ModalOptionProps & {
  offerIds: string[];
  title?: string;
  description?: string;
  itemName: string;
  additionalInfo?: string;
};

export const RemoveItemModal = () => {
  const { t } = useTranslation();
  const { modalOption, closeModal } = useModalStore();
  const { addToCart } = useCartStore();
  const {
    offerIds,
    title = t('client.cart.removeModalTitle'),
    description,
    itemName,
    additionalInfo,
  } = modalOption as RemoveModalOptions;

  if (!offerIds || offerIds.length === 0) {
    return null;
  }

  const handleRemove = async () => {
    const offers = offerIds.map((productOfferId) => ({
      productOfferId,
      quantity: 0,
    }));

    addToCart({
      offers,
      onError: () => {},
    });
    closeModal();
  };

  return (
    <Modal name={MODAL_NAME.REMOVE_PRODUCT_FROM_CART} size="md">
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <BinIcon />
          <span className="mt-4 mb-6 text-2xl font-bold">{title}</span>
          {description ? (
            <span className="mb-4">{description}</span>
          ) : (
            <span className={additionalInfo ? 'mb-2' : 'mb-4'}>
              <Trans
                i18nKey="client.cart.removeModalText"
                values={{ itemName }}
                components={{ strong: <strong /> }}
              />
            </span>
          )}
          {additionalInfo && (
            <span className="mb-4 text-sm text-gray-500">{additionalInfo}</span>
          )}
        </div>
        <div className={styles.modalFooter}>
          <Button onClick={closeModal} variant="default" fullWidth>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleRemove} fullWidth>
            {t('common.remove')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
