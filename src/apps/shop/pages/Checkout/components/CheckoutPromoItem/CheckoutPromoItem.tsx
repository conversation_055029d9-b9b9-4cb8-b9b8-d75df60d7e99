import { getPriceString } from '@/utils';
import { BuyXGetYPromotionData } from '@/libs/promotions/types';
import { PROMO_TYPE } from '@/constants';

type CheckoutPromoItemProps = {
  promoItem: BuyXGetYPromotionData;
};

export const CheckoutPromoItem = ({ promoItem }: CheckoutPromoItemProps) => {
  if (!promoItem || !promoItem.promotion || !promoItem.freeOffer) return null;

  return (
    <div className="rounded-lg text-sm">
      <div className="flex flex-row items-center gap-4 border-b border-gray-100 p-4">
        <div className="min-w-0 flex-1">
          <div className="mb-1">
            <span className="font-medium text-green-700">
              Promotion • {PROMO_TYPE[promoItem.promotion.type]}
            </span>
          </div>
          <h3 className="text-sm leading-tight font-medium break-words text-gray-900">
            {promoItem.promotion.name}
          </h3>
        </div>
        <div className="grid grid-cols-[auto_auto_auto]">
          <span className="text-gray-500">
            Total:{' '}
            <span className="font-semibold text-gray-900">
              {getPriceString(promoItem.subtotalAllItems)}
            </span>
          </span>
          <div className="divider-v"></div>
          <span className="text-gray-500">
            Net Total:{' '}
            <span className="font-semibold text-gray-900">
              {getPriceString(promoItem.subtotalPaidItems)}
            </span>
          </span>
        </div>
      </div>

      <div className="bg-[#B6F5F926] p-4">
        {promoItem.items.map((item) => (
          <div key={item.id} className="mb-4 last:mb-0">
            <div className="grid grid-cols-[2rem_1fr_auto] items-center gap-3 py-2">
              <div className="flex h-5 w-7 items-center justify-center rounded bg-cyan-100 font-medium text-gray-900">
                {item.quantity}
              </div>
              <span className="flex-1 font-medium text-gray-900">
                {item.product.name}
              </span>
              <span className="font-medium text-gray-900">
                {getPriceString(item.subtotal)}
              </span>
            </div>
            {item.freeItemsQty > 0 && item.freeOffer && (
              <div className="mt-1 grid grid-cols-[2rem_1fr_auto] items-center gap-3 py-1">
                <div className="flex h-5 w-7 items-center justify-center rounded bg-cyan-100 font-medium text-gray-900">
                  {item.freeItemsQty}
                </div>
                <span className="flex-1 font-medium text-gray-900">
                  {item.freeOffer.name || item.product.name}
                </span>
                <span className="font-medium text-green-700">Free</span>
              </div>
            )}
          </div>
        ))}

        <div className="mt-4 flex flex-row items-center justify-between border-t border-black/10 pt-4">
          <span className="text-gray-500">
            You&apos;re getting total of{' '}
            <span className="font-medium text-gray-900">
              {promoItem.paidItemsQty + promoItem.freeItemsQty}
            </span>{' '}
            products
          </span>
          <span className="text-gray-500">
            Promotional Savings:{' '}
            <span className="font-medium text-gray-900">
              {getPriceString(
                promoItem.subtotalAllItems - promoItem.subtotalPaidItems,
              )}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
};
