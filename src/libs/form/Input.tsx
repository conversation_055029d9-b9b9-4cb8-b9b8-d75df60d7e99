import {
  forwardRef,
  InputHTMLAttributes,
  ReactElement,
  ReactNode,
  useState,
  useId,
} from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils';
import EyeIcon from './assets/eye.svg?react';
import EyeCrossIcon from './assets/eye-cross.svg?react';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Button } from '@/libs/ui/Button/Button';
import { HelpTooltip } from '../ui/HelpTooltip/HelpTooltip';

const inputVariants = cva(
  [
    'box-border w-full rounded border border-gray-300 bg-white px-3 text-sm',
    'focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-yellow-300',
    'disabled:bg-gray-100',
    '[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none',
  ],
  {
    variants: {
      size: {
        sm: 'h-8',
        md: 'h-10',
        lg: 'h-12',
      },
      hasError: {
        true: 'border-red-500',
        false: 'border-gray-300',
      },
    },
    defaultVariants: {
      size: 'md',
      hasError: false,
    },
  },
);

const rightSectionVariants = cva('absolute right-3 top-1/2 -translate-y-1/2');

export type InputProps = {
  id?: string;
  label?: string | ReactNode;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
  mask?: (value: string) => string;
  tooltip?: string;
  rightSection?: ReactElement;
  className?: string;
} & Omit<InputHTMLAttributes<HTMLInputElement>, 'size' | 'className'> &
  VariantProps<typeof inputVariants>;

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      id,
      label,
      error,
      mask,
      size = 'md',
      align = 'left',
      type: originalType,
      onChange,
      tooltip,
      rightSection,
      className,
      ...rest
    },
    ref,
  ) => {
    const generatedId = useId();
    const inputId = id || generatedId;
    const [type, setType] = useState(originalType);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (mask) {
        e.target.value = mask(e.target.value);
      }

      if (onChange) {
        onChange(e);
      }
    };

    const handleSwitchType = () => {
      setType((type) => (type === 'password' ? 'text' : 'password'));
    };

    return (
      <div className="relative flex w-full flex-col">
        {label || tooltip ? (
          <Flex gap="0.5rem" align="center" mb="0.4rem">
            {label && (
              <label htmlFor={inputId} className="text-sm">
                {label}
              </label>
            )}
            {tooltip && <HelpTooltip message={tooltip} />}
          </Flex>
        ) : null}
        <div className="relative">
          <input
            id={inputId}
            ref={(inputRef) => {
              if (typeof ref === 'function') {
                ref(inputRef);
              } else if (ref && 'current' in ref) {
                ref.current = inputRef;
              }

              if (mask && inputRef) {
                inputRef.value = mask(inputRef.value);
              }
            }}
            onChange={handleChange}
            aria-invalid={!!error}
            style={{ textAlign: align }}
            type={type}
            {...rest}
            className={mergeClasses(
              inputVariants({ size, hasError: !!error }),
              className,
            )}
          />
          {rightSection ? (
            <div className={rightSectionVariants()}>{rightSection}</div>
          ) : originalType === 'password' ? (
            <Button
              variant="unstyled"
              type="button"
              onClick={handleSwitchType}
              className={rightSectionVariants()}
            >
              {type === 'password' ? <EyeIcon /> : <EyeCrossIcon />}
            </Button>
          ) : null}
        </div>
        {error && (
          <p className="mt-1.5 max-w-full text-xs text-red-700">{error}</p>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';
