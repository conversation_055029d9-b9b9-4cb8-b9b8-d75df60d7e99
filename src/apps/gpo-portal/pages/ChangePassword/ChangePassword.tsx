import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { ResetPasswordForm } from '@/libs/auth/components/ResetPasswordForm/ResetPasswordForm';
import { fetchApi } from '@/libs/utils/api';
import { successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

export const ChangePassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const changePasswordApiFunc = async (values?: {
    password: string;
    confirmPassword: string;
  }) => {
    if (!values) return;

    await fetchApi('/gpo/password-resets/confirm', {
      method: 'POST',
      body: values,
    });

    successNotification(t('gpo.changePassword.successMessage'));
  };

  return (
    <ResetPasswordForm
      apiFunc={changePasswordApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.login)}
      schema={SCHEMA}
      namespace="gpo.changePassword"
    />
  );
};
