import { CartItemType } from '@/libs/cart/types';
import { getPriceString } from '@/utils';

export const CheckoutItem = ({ item }: { item: CartItemType }) => {
  const { quantity, product, subtotal, price } = item;

  return (
    <div className="grid grid-cols-[1fr_auto] gap-3 p-3">
      <span className="font-medium text-gray-900">{product?.name}</span>

      <div className="flex">
        <span className="text-gray-500">
          Quantity:{' '}
          <span className="font-medium text-gray-900">{quantity}</span>
        </span>
        <div className="divider-v"></div>

        <span className="text-gray-500">
          Unit:{' '}
          <span className="font-medium text-gray-900">
            {getPriceString(price)}
          </span>
        </span>
        <div className="divider-v"></div>

        <span className="text-gray-500">
          Net Total:{' '}
          <span className="font-medium text-gray-900">
            {getPriceString(subtotal)}
          </span>
        </span>
      </div>
    </div>
  );
};
