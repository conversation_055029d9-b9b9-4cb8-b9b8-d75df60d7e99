import { create } from 'zustand';
import { UserType } from '@/types/common';
import { fetchApi } from '@/libs/utils/api';
import { tokenManager } from '@/libs/auth/utils/tokenManager';

interface AuthState {
  user: UserType | null;
  setUser: (user: UserType | null) => void;
  logout: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  logout: async () => {
    try {
      await fetchApi('/gpo/logout', {
        withApi: false,
        authStrategy: 'token',
      });
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear token and user state
      tokenManager.removeToken();
      set({ user: null });
    }
  },
}));
