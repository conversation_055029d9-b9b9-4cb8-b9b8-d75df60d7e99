import { VendorType } from '@/types';
import { Dayjs } from 'dayjs';

export interface GpoVendorData {
  amountUntilGoal: number;
  goalAmount: number;
  growthTargetPercentage: number;
  id: string;
  imageUrl: string;
  marketSharePercentage: number;
  name: string;
  notification: string;
  totalSpend: number;
  type: VendorType['type'];
}

export type VendorsOverviewResponse = {
  total_spend: number;
  vendors: GpoVendorData[];
};

export interface VendorsOverviewFilters {
  date_from?: Dayjs;
  date_to?: Dayjs;
  vendor_type?: string;
  status?: 'all' | 'connected' | 'disconnected';
  min_spend?: number;
  max_spend?: number;
}
